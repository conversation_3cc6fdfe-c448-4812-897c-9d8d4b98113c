# BC Monitoring Service — End-to-End Test Matrix

## 1. Service Initialization & Startup
### 1.1 Normal Cases
#### 1.1.1 Successful Service Startup
- **Description**: Service starts successfully with all dependencies available
- **Input**: Valid environment variables, accessible S3 bucket with valid ABI files, accessible DynamoDB, accessible Ethereum WebSocket endpoint
- **Expected Result**: Service logs "started bc monitoring", begins monitoring blockchain events

#### 1.1.2 Service Restart After WebSocket Error
- **Description**: Service automatically restarts monitoring after WebSocket handshake error
- **Input**: WebSocket connection fails with "rpc.wsHandshakeError"
- **Expected Result**: Service logs "restart bc monitoring", reinitializes monitor interactor, continues operation

### 1.2 Semi-Normal Cases
#### 1.2.1 Service Startup with Empty ABI Bucket
- **Description**: Service starts when S3 bucket exists but contains no ABI files
- **Input**: Empty S3 bucket, valid other dependencies
- **Expected Result**: Service starts successfully but cannot monitor any contract events

#### 1.2.2 Service Startup with Missing Block Height Record
- **Description**: Service starts when DynamoDB BlockHeight table is empty
- **Input**: Empty BlockHeight table in DynamoDB
- **Expected Result**: Service starts monitoring from block 1 (blockHeight 0 + 1)

### 1.3 Abnormal Cases
#### 1.3.1 Service Startup Failure - Invalid Environment Variables
- **Description**: Service fails to start due to missing or invalid environment variables
- **Input**: Missing WEBSOCKET_URI_HOST, WEBSOCKET_URI_PORT, or other required env vars
- **Expected Result**: Service exits with fatal error, logs configuration error

#### 1.3.2 Service Startup Failure - DynamoDB Connection Error
- **Description**: Service fails to start due to DynamoDB connection issues
- **Input**: Invalid DynamoDB endpoint or credentials
- **Expected Result**: Service exits with fatal error during monitor interactor initialization

## 2. ABI File Management
### 2.1 Normal Cases
#### 2.1.1 Successful ABI Download and Parsing
- **Description**: Downloads and parses valid ABI files from S3
- **Input**: S3 bucket with valid JSON ABI files in correct directory structure (e.g., "3000/Contract.json")
- **Expected Result**: ABI files parsed successfully, contract addresses and events stored in memory

#### 2.1.2 Multiple Zone ABI Processing
- **Description**: Processes ABI files from multiple zones (3000, 3001, etc.)
- **Input**: S3 bucket with ABI files in multiple zone directories
- **Expected Result**: All zone ABI files processed, contracts from all zones available for monitoring

#### 2.1.3 Hardhat vs Truffle ABI Format Handling
- **Description**: Correctly parses ABI files based on ABI_FORMAT environment variable
- **Input**: ABI files with different address field locations, ABI_FORMAT set to "hardhat" or "truffle"
- **Expected Result**: Contract addresses extracted from correct JSON path based on format

### 2.2 Semi-Normal Cases
#### 2.2.1 Non-JSON Files in S3 Bucket
- **Description**: Skips non-JSON files in S3 bucket
- **Input**: S3 bucket containing .txt, .md, or other non-.json files
- **Expected Result**: Non-JSON files skipped with log message, JSON files processed normally

#### 2.2.2 Nested Directory Structure
- **Description**: Processes only direct subdirectory files, skips deeply nested files
- **Input**: S3 bucket with files in nested directories (e.g., "3000/subdir/Contract.json")
- **Expected Result**: Deeply nested files skipped, only direct zone subdirectory files processed

### 2.3 Abnormal Cases
#### 2.3.1 S3 Bucket Access Denied
- **Description**: Service fails to start when S3 bucket is inaccessible
- **Input**: S3 bucket with access denied or invalid credentials
- **Expected Result**: Service exits with fatal error "failed to list s3 CommonPrefixes objects"

#### 2.3.2 Invalid JSON File Content
- **Description**: Service fails to start when ABI file contains malformed JSON
- **Input**: S3 bucket with corrupted or invalid JSON files
- **Expected Result**: Service exits with fatal error "failed to unmarshal abi json"

#### 2.3.3 Missing ABI Field in JSON
- **Description**: Service fails when JSON file lacks required "abi" field
- **Input**: Valid JSON file without "abi" field or with empty "abi" field
- **Expected Result**: Service exits with fatal error during ABI unmarshaling

#### 2.3.4 S3 Network Timeout
- **Description**: Service fails when S3 operations timeout
- **Input**: S3 operations that exceed timeout limits
- **Expected Result**: Service exits with fatal error, timeout error logged

## 3. Blockchain Event Monitoring
### 3.1 Normal Cases
#### 3.1.1 New Block Event Detection
- **Description**: Detects and processes events from new blockchain blocks
- **Input**: New blocks with contract events matching loaded ABI definitions
- **Expected Result**: Events extracted, parsed, and saved to DynamoDB with correct transaction hash, log index, and event data

#### 3.1.2 Pending Transaction Processing
- **Description**: Processes pending transactions from specified block height
- **Input**: Pending transactions in blockchain from last processed block height + 1
- **Expected Result**: Pending transactions processed, events saved, block height updated

#### 3.1.3 Event Data Parsing and Storage
- **Description**: Correctly parses indexed and non-indexed event parameters
- **Input**: Contract events with various parameter types (indexed/non-indexed)
- **Expected Result**: Event data correctly parsed into IndexedValues and NonIndexedValues JSON strings

#### 3.1.4 TraceId Extraction
- **Description**: Extracts traceId from event non-indexed values when present
- **Input**: Events containing traceId in non-indexed values
- **Expected Result**: TraceId correctly extracted and logged with event processing

### 3.2 Semi-Normal Cases
#### 3.2.1 Block with No Matching Events
- **Description**: Processes blocks that contain no events matching loaded ABIs
- **Input**: Blockchain blocks with transactions but no events matching contract addresses
- **Expected Result**: Block processed without errors, no events saved, block height updated

#### 3.2.2 Event Without TraceId
- **Description**: Processes events that don't contain traceId in non-indexed values
- **Input**: Contract events without traceId field
- **Expected Result**: Event processed normally, empty traceId logged

#### 3.2.3 Block Timestamp Validation
- **Description**: Validates block timestamps against allowable difference threshold
- **Input**: Blocks with timestamps within ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC
- **Expected Result**: Blocks processed normally when timestamp difference is acceptable

### 3.3 Abnormal Cases
#### 3.3.1 WebSocket Connection Failure
- **Description**: Handles WebSocket connection failures during monitoring
- **Input**: WebSocket connection drops or fails during event subscription
- **Expected Result**: Error logged, monitoring stops, service attempts restart via retry mechanism

#### 3.3.2 Event with Empty Transaction Hash
- **Description**: Rejects events with empty or missing transaction hash
- **Input**: Blockchain events with empty transaction hash
- **Expected Result**: Event rejected, error logged "event transaction hash is zero", monitoring continues with retry

#### 3.3.3 Block Number Zero Detection
- **Description**: Handles blocks with zero block number
- **Input**: Blockchain events with BlockNumber = 0
- **Expected Result**: Block rejected, warning logged "block height Number is zero" (WARN level), monitoring restarts after interval using goto RETRY pattern

#### 3.3.4 ABI Event Definition Not Found
- **Description**: Handles events that don't match any loaded ABI definitions
- **Input**: Blockchain events from contracts not in loaded ABI store
- **Expected Result**: ErrNoAbiEventFound error (not logged), event skipped, monitoring continues

## 4. Data Persistence
### 4.1 Normal Cases
#### 4.1.1 Event Storage to DynamoDB
- **Description**: Successfully stores parsed events to DynamoDB Events table
- **Input**: Valid parsed events with all required fields
- **Expected Result**: Events stored in DynamoDB with correct table name prefix, success logged

#### 4.1.2 Block Height Update
- **Description**: Updates block height after successful event processing
- **Input**: Successfully processed block with events
- **Expected Result**: Block height updated in DynamoDB BlockHeight table with ID=1

#### 4.1.3 Concurrent Event Processing
- **Description**: Handles multiple events from same transaction/block
- **Input**: Multiple events from single transaction or block
- **Expected Result**: All events processed and stored individually with correct log indexes

#### 4.1.4 Pending vs Regular Transaction Processing
- **Description**: Different block height update behavior for pending vs regular transactions
- **Input**: Mix of pending transactions and regular blockchain events
- **Expected Result**: Pending transactions save events but do NOT update block height; regular transactions save events AND update block height

### 4.2 Semi-Normal Cases
#### 4.2.1 DynamoDB Connection Pool Management
- **Description**: Manages DynamoDB connection pool under load
- **Input**: High volume of events requiring database operations
- **Expected Result**: Connection pool (max 10 connections) managed correctly, operations complete successfully

#### 4.2.2 Empty Events List Processing
- **Description**: Handles transactions with empty events list
- **Input**: Transactions containing empty Events array
- **Expected Result**: Transaction processed without errors, block height updated, no events stored

### 4.3 Abnormal Cases
#### 4.3.1 DynamoDB Write Failure
- **Description**: Handles DynamoDB write failures during event storage
- **Input**: DynamoDB PutItem operations that fail due to service issues
- **Expected Result**: Event save returns false, error logged, monitoring stops and retries after interval

#### 4.3.2 DynamoDB Read Failure
- **Description**: Handles DynamoDB read failures when getting block height
- **Input**: DynamoDB Query operations that fail when retrieving current block height
- **Expected Result**: Error logged "failed to get blockheight", monitoring stops and returns error

#### 4.3.3 DynamoDB Serialization Error
- **Description**: Handles errors during DynamoDB attribute serialization
- **Input**: Event or BlockHeight entities that cannot be serialized to DynamoDB format
- **Expected Result**: Serialization error logged, operation fails, monitoring stops

#### 4.3.4 Block Height Save Failure
- **Description**: Handles failures when updating block height
- **Input**: DynamoDB PutItem failure for BlockHeight entity
- **Expected Result**: Block height save returns false, error logged, monitoring stops and retries after interval

## 5. Configuration Management
### 5.1 Normal Cases
#### 5.1.1 Environment Variable Loading
- **Description**: Correctly loads all required environment variables
- **Input**: All required environment variables set with valid values
- **Expected Result**: Configuration values accessible throughout application

#### 5.1.2 Local vs Production Environment Handling
- **Description**: Correctly configures services based on ENV variable
- **Input**: ENV set to "local" or "prod"
- **Expected Result**: Appropriate S3 and DynamoDB endpoints configured

### 5.2 Semi-Normal Cases
#### 5.2.1 Default Configuration Values
- **Description**: Uses default values when optional environment variables are missing
- **Input**: Missing optional configuration like table name prefix
- **Expected Result**: Service uses default values, operates normally

### 5.3 Abnormal Cases
#### 5.3.1 Invalid Subscription Check Interval
- **Description**: Handles invalid SUBSCRIPTION_CHECK_INTERVAL value
- **Input**: SUBSCRIPTION_CHECK_INTERVAL set to non-numeric value
- **Expected Result**: Service fails with error "faild to convert checkInterval" (note: typo in actual code), monitoring stops

#### 5.3.2 Invalid Block Timestamp Difference Setting
- **Description**: Handles invalid ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC value
- **Input**: ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC set to non-numeric value
- **Expected Result**: Service fails during event log DAO initialization, monitoring cannot start

## 6. Error Handling & Recovery
### 6.1 Normal Cases
#### 6.1.1 Retry Mechanism for WebSocket Errors
- **Description**: Automatically retries WebSocket connections on specific errors
- **Input**: WebSocket handshake errors ("rpc.wsHandshakeError")
- **Expected Result**: Service retries up to 5 times with 3-nanosecond delays (effectively instant), reinitializes monitor on retry

### 6.2 Semi-Normal Cases
#### 6.2.1 Graceful Degradation on Non-Critical Errors
- **Description**: Continues operation when non-critical errors occur
- **Input**: Events that cannot be parsed due to missing ABI definitions
- **Expected Result**: Specific events skipped, monitoring continues for other events

#### 6.2.2 Internal Retry Pattern with goto RETRY
- **Description**: Service uses internal goto RETRY pattern for recoverable errors within monitoring loop
- **Input**: DynamoDB save failures, block number zero, empty transaction hash
- **Expected Result**: Service cancels current context, sleeps for checkInterval milliseconds, restarts monitoring from RETRY label (separate from main retry mechanism)

### 6.3 Abnormal Cases
#### 6.3.1 Fatal Error Propagation
- **Description**: Properly propagates fatal errors that require service restart
- **Input**: Critical errors like DynamoDB connection failures, ABI parsing failures
- **Expected Result**: Errors propagated to main function, service exits with l.Fatalln()

#### 6.3.2 Non-Retryable Error Handling
- **Description**: Handles errors that should not trigger retry mechanism
- **Input**: Errors other than "rpc.wsHandshakeError"
- **Expected Result**: Service logs warning "restarting bc monitoring", continues infinite retry loop

## 7. Logging & Monitoring
### 7.1 Normal Cases
#### 7.1.1 Structured Logging with Context
- **Description**: Logs include relevant context fields for debugging
- **Input**: Various service operations
- **Expected Result**: Logs include fields like tx_hash, block_height, event_name, trace_id

#### 7.1.2 Log Level Management
- **Description**: Appropriate log levels used for different scenarios
- **Input**: Various service events and errors
- **Expected Result**: Info for normal operations, Error for failures, Warn for retries

### 7.2 Semi-Normal Cases
#### 7.2.1 High Volume Logging Performance
- **Description**: Logging performance under high event volume
- **Input**: High frequency of blockchain events
- **Expected Result**: Logging doesn't significantly impact processing performance

### 7.3 Abnormal Cases
#### 7.3.1 Logging System Failure
- **Description**: Service behavior when logging system fails
- **Input**: Logger initialization or write failures
- **Expected Result**: Service continues operation, may exit if logger cannot be initialized
